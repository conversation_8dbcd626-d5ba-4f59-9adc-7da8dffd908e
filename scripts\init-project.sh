#!/bin/bash

# 爱情个人软件 - 项目初始化脚本
# Bash脚本，用于快速搭建开发环境 (Linux/macOS)

echo "💕 爱情个人软件 - 项目初始化"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 检查必要的工具
echo -e "${YELLOW}🔍 检查开发环境...${NC}"

# 检查Go版本
if command -v go &> /dev/null; then
    GO_VERSION=$(go version)
    echo -e "${GREEN}✅ Go: $GO_VERSION${NC}"
else
    echo -e "${RED}❌ Go未安装，请先安装Go 1.21+${NC}"
    exit 1
fi

# 检查Node.js版本
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    echo -e "${GREEN}✅ Node.js: $NODE_VERSION${NC}"
else
    echo -e "${RED}❌ Node.js未安装，请先安装Node.js 16+${NC}"
    exit 1
fi

# 检查Wails CLI
if command -v wails &> /dev/null; then
    WAILS_VERSION=$(wails version)
    echo -e "${GREEN}✅ Wails: $WAILS_VERSION${NC}"
else
    echo -e "${YELLOW}⚠️  Wails CLI未安装，正在安装...${NC}"
    go install github.com/wailsapp/wails/v2/cmd/wails@latest
    echo -e "${GREEN}✅ Wails CLI安装完成${NC}"
fi

# 创建项目目录结构
echo -e "${YELLOW}📁 创建项目目录结构...${NC}"

directories=(
    "backend/models"
    "backend/services" 
    "backend/storage"
    "frontend/src/assets/images"
    "frontend/src/assets/fonts"
    "frontend/src/components"
    "frontend/src/pages"
    "frontend/src/styles"
    "frontend/src/utils"
    "data/config"
    "data/couple"
    "data/anniversaries"
    "data/photos/images/avatars"
    "data/photos/images/thumbnails"
    "data/chats"
    "data/gifts"
    "data/diary"
    "data/mood"
    "data/backup/auto"
    "data/backup/manual"
    "docs"
    "scripts"
)

for dir in "${directories[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        echo -e "${GREEN}  ✅ 创建目录: $dir${NC}"
    fi
done

# 初始化Wails项目
echo -e "${YELLOW}🚀 初始化Wails项目...${NC}"

if [ ! -f "wails.json" ]; then
    # 创建wails.json配置文件
    cat > wails.json << 'EOF'
{
  "$schema": "https://wails.io/schemas/config.v2.json",
  "name": "love-software",
  "outputfilename": "爱情个人软件",
  "frontend": {
    "dir": "./frontend",
    "install": "npm install",
    "build": "npm run build",
    "dev": "npm run dev",
    "package": {
      "manager": "npm"
    }
  },
  "backend": {
    "dir": "./backend"
  },
  "author": {
    "name": "Love Software Developer",
    "email": "<EMAIL>"
  },
  "info": {
    "companyName": "Love Software",
    "productName": "爱情个人软件",
    "productVersion": "1.0.0",
    "copyright": "Copyright © 2024 Love Software. All rights reserved.",
    "comments": "基于Go和Wails v2开发的爱情管理软件"
  },
  "nsisType": "multiple"
}
EOF
    echo -e "${GREEN}  ✅ 创建wails.json配置文件${NC}"
fi

# 创建Go模块
echo -e "${YELLOW}📦 初始化Go模块...${NC}"

if [ ! -f "go.mod" ]; then
    go mod init love-software
    echo -e "${GREEN}  ✅ 创建go.mod文件${NC}"
fi

# 安装Go依赖
echo -e "${YELLOW}📥 安装Go依赖...${NC}"

go_dependencies=(
    "github.com/wailsapp/wails/v2@latest"
    "github.com/go-redis/redis/v8"
    "golang.org/x/crypto"
)

for dep in "${go_dependencies[@]}"; do
    echo -e "${CYAN}  📦 安装: $dep${NC}"
    go get "$dep"
done

# 创建前端package.json
echo -e "${YELLOW}📄 创建前端配置文件...${NC}"

cat > frontend/package.json << 'EOF'
{
  "name": "love-software-frontend",
  "version": "1.0.0",
  "description": "爱情个人软件前端",
  "main": "index.html",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  },
  "devDependencies": {
    "vite": "^5.0.0",
    "@vitejs/plugin-legacy": "^5.0.0",
    "autoprefixer": "^10.4.16",
    "postcss": "^8.4.32",
    "tailwindcss": "^3.3.6"
  },
  "dependencies": {
    "font-awesome": "^4.7.0"
  }
}
EOF
echo -e "${GREEN}  ✅ 创建package.json文件${NC}"

# 创建前端入口文件
cat > frontend/index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💕 爱情个人软件</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'PingFang SC', 'Microsoft YaHei', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        'primary-pink': '#ff6b9d',
                        'primary-purple': '#a855f7',
                        'secondary-blue': '#3b82f6',
                        'accent-gold': '#fbbf24',
                    }
                }
            }
        }
    </script>
    <style>
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', 'PingFang SC', 'Microsoft YaHei', system-ui, sans-serif;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="min-h-screen flex items-center justify-center p-8">
            <div class="glass-card p-8 max-w-md w-full text-center">
                <div class="text-6xl mb-4">💕</div>
                <h1 class="text-3xl font-bold text-white mb-4">爱情个人软件</h1>
                <p class="text-white/80 mb-6">基于 Go + Wails v2 开发</p>
                <button class="glass-button px-6 py-3 rounded-lg text-white font-medium w-full">
                    <i class="fas fa-heart mr-2"></i>
                    开始使用
                </button>
            </div>
        </div>
    </div>
    <script type="module" src="/src/main.js"></script>
</body>
</html>
EOF
echo -e "${GREEN}  ✅ 创建index.html文件${NC}"

# 创建基础的main.js文件
cat > frontend/src/main.js << 'EOF'
// 爱情个人软件 - 前端入口文件
console.log('💕 爱情个人软件启动中...')

// 等待Wails运行时准备就绪
window.addEventListener('DOMContentLoaded', () => {
    console.log('✅ 前端界面加载完成')
    
    // 初始化应用
    initApp()
})

function initApp() {
    console.log('🚀 初始化应用...')
    
    // 这里将添加应用初始化逻辑
    // 包括路由、状态管理、组件初始化等
}

// 导出给Wails使用的函数
window.showMessage = function(message) {
    alert(message)
}
EOF
echo -e "${GREEN}  ✅ 创建main.js文件${NC}"

# 创建基础的Go后端文件
echo -e "${YELLOW}🔧 创建Go后端文件...${NC}"

cat > main.go << 'EOF'
package main

import (
	"context"
	"embed"
	"fmt"
	"log"

	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/options/assetserver"
)

//go:embed all:frontend/dist
var assets embed.FS

// App struct
type App struct {
	ctx context.Context
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{}
}

// startup is called when the app starts. The context passed
// is saved so we can call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx
	fmt.Println("💕 爱情个人软件后端启动成功")
}

// Greet returns a greeting for the given name
func (a *App) Greet(name string) string {
	return fmt.Sprintf("Hello %s, It's show time!", name)
}

func main() {
	// Create an instance of the app structure
	app := NewApp()

	// Create application with options
	err := wails.Run(&options.App{
		Title:  "💕 爱情个人软件",
		Width:  1200,
		Height: 800,
		AssetServer: &assetserver.Options{
			Assets: assets,
		},
		BackgroundColour: &options.RGBA{R: 27, G: 38, B: 54, A: 1},
		OnStartup:        app.startup,
		Bind: []interface{}{
			app,
		},
	})

	if err != nil {
		log.Fatal("Error:", err)
	}
}
EOF
echo -e "${GREEN}  ✅ 创建main.go文件${NC}"

# 设置脚本执行权限
chmod +x scripts/init-project.sh

# 安装前端依赖
echo -e "${YELLOW}📥 安装前端依赖...${NC}"
cd frontend
npm install
cd ..
echo -e "${GREEN}  ✅ 前端依赖安装完成${NC}"

# 完成提示
echo ""
echo -e "${GREEN}🎉 项目初始化完成！${NC}"
echo -e "${GREEN}================================${NC}"
echo ""
echo -e "${YELLOW}📋 下一步操作:${NC}"
echo -e "${CYAN}  1. 开发模式运行: wails dev${NC}"
echo -e "${CYAN}  2. 构建生产版本: wails build${NC}"
echo -e "${CYAN}  3. 查看项目文档: docs/${NC}"
echo ""
echo -e "${YELLOW}📁 项目结构已创建完成，包含:${NC}"
echo -e "${GREEN}  ✅ Wails配置文件${NC}"
echo -e "${GREEN}  ✅ Go后端基础结构${NC}"  
echo -e "${GREEN}  ✅ 前端基础文件${NC}"
echo -e "${GREEN}  ✅ 数据存储目录${NC}"
echo -e "${GREEN}  ✅ 完整项目文档${NC}"
echo ""
echo -e "${MAGENTA}💕 开始你的爱情软件开发之旅吧！${NC}"
