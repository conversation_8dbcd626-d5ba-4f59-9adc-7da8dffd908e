# 💾 数据存储设计文档 - Data Storage Design

## 🎯 存储方案概述

本项目支持两种数据存储方案：
1. **JSON本地文件存储** (默认方案)
2. **Redis数据存储** (可选方案)

## 📁 本地JSON存储方案

### 存储结构
```
data/
├── config/
│   ├── app.json          # 应用配置
│   └── user.json         # 用户设置
├── couple/
│   └── profile.json      # 情侣档案
├── anniversaries/
│   └── events.json       # 纪念日数据
├── photos/
│   ├── albums.json       # 相册索引
│   └── images/           # 图片文件夹
│       ├── 2024/
│       └── thumbnails/
├── chats/
│   ├── 2024-01.json     # 按月分割的聊天记录
│   └── 2024-02.json
├── gifts/
│   └── records.json      # 礼物记录
├── diary/
│   ├── 2024-01.json     # 按月分割的日记
│   └── 2024-02.json
├── mood/
│   └── records.json      # 心情记录
└── backup/
    ├── auto/            # 自动备份
    └── manual/          # 手动备份
```

### 数据模型设计

#### 1. 应用配置 (config/app.json)
```json
{
  "version": "1.0.0",
  "dataVersion": "1.0",
  "createdAt": "2024-01-01T00:00:00Z",
  "lastModified": "2024-01-15T12:30:00Z",
  "settings": {
    "theme": "glassmorphism",
    "language": "zh-CN",
    "autoBackup": true,
    "backupInterval": 24,
    "dataEncryption": true,
    "reminderEnabled": true
  },
  "storage": {
    "type": "local",
    "maxFileSize": "10MB",
    "compressionEnabled": true,
    "encryptionKey": "encrypted_key_hash"
  }
}
```

#### 2. 情侣档案 (couple/profile.json)
```json
{
  "id": "couple_uuid",
  "createdAt": "2024-01-01T00:00:00Z",
  "lastModified": "2024-01-15T12:30:00Z",
  "partner1": {
    "id": "partner1_uuid",
    "name": "张三",
    "nickname": "小三",
    "birthday": "1995-05-20",
    "avatar": "images/avatars/partner1.jpg",
    "preferences": ["电影", "旅行", "美食"],
    "contact": {
      "phone": "encrypted_phone",
      "email": "encrypted_email"
    }
  },
  "partner2": {
    "id": "partner2_uuid",
    "name": "李四",
    "nickname": "小四",
    "birthday": "1996-08-15",
    "avatar": "images/avatars/partner2.jpg",
    "preferences": ["音乐", "读书", "运动"],
    "contact": {
      "phone": "encrypted_phone",
      "email": "encrypted_email"
    }
  },
  "relationship": {
    "startDate": "2020-02-14",
    "status": "恋爱中",
    "milestones": [
      {
        "id": "milestone_uuid",
        "date": "2020-02-14",
        "event": "第一次约会",
        "description": "在咖啡厅的美好相遇",
        "photos": ["images/milestones/first_date.jpg"]
      }
    ]
  }
}
```

#### 3. 纪念日数据 (anniversaries/events.json)
```json
{
  "anniversaries": [
    {
      "id": "anniversary_uuid",
      "title": "恋爱一周年",
      "date": "2021-02-14",
      "type": "anniversary",
      "recurring": "yearly",
      "reminderDays": [7, 3, 1],
      "description": "我们在一起的第一个年头",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00Z",
      "lastReminded": "2024-02-07T09:00:00Z"
    }
  ],
  "reminders": [
    {
      "id": "reminder_uuid",
      "anniversaryId": "anniversary_uuid",
      "reminderDate": "2024-02-11T09:00:00Z",
      "status": "pending",
      "message": "还有3天就是恋爱一周年了！"
    }
  ]
}
```

#### 4. 相册数据 (photos/albums.json)
```json
{
  "albums": [
    {
      "id": "album_uuid",
      "name": "2024年春游",
      "description": "春天的美好回忆",
      "coverPhoto": "images/2024/03/cover.jpg",
      "createdAt": "2024-03-15T00:00:00Z",
      "lastModified": "2024-03-20T15:30:00Z",
      "tags": ["旅行", "春天", "户外"],
      "photoCount": 25,
      "photos": [
        {
          "id": "photo_uuid",
          "filename": "IMG_001.jpg",
          "originalPath": "images/2024/03/IMG_001.jpg",
          "thumbnailPath": "images/thumbnails/IMG_001_thumb.jpg",
          "takenAt": "2024-03-15T14:30:00Z",
          "uploadedAt": "2024-03-15T20:00:00Z",
          "description": "在樱花树下的合影",
          "tags": ["樱花", "合影"],
          "location": "公园",
          "fileSize": 2048576,
          "dimensions": {
            "width": 1920,
            "height": 1080
          }
        }
      ]
    }
  ],
  "tags": [
    {
      "name": "旅行",
      "count": 45,
      "color": "#3b82f6"
    },
    {
      "name": "约会",
      "count": 32,
      "color": "#ff6b9d"
    }
  ]
}
```

### 数据加密策略

#### 加密实现
```go
package storage

import (
    "crypto/aes"
    "crypto/cipher"
    "crypto/rand"
    "encoding/base64"
    "io"
)

type Encryptor struct {
    key []byte
}

func NewEncryptor(password string) *Encryptor {
    // 使用PBKDF2从密码生成密钥
    key := pbkdf2.Key([]byte(password), []byte("love_salt"), 4096, 32, sha256.New)
    return &Encryptor{key: key}
}

func (e *Encryptor) Encrypt(plaintext []byte) (string, error) {
    block, err := aes.NewCipher(e.key)
    if err != nil {
        return "", err
    }

    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return "", err
    }

    nonce := make([]byte, gcm.NonceSize())
    if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
        return "", err
    }

    ciphertext := gcm.Seal(nonce, nonce, plaintext, nil)
    return base64.StdEncoding.EncodeToString(ciphertext), nil
}
```

### 备份策略

#### 自动备份配置
```json
{
  "backup": {
    "enabled": true,
    "interval": 24,
    "maxBackups": 30,
    "compression": true,
    "encryption": true,
    "location": "data/backup/auto/",
    "schedule": "0 2 * * *"
  }
}
```

#### 备份文件命名
```
backup_2024-01-15_02-00-00.zip
├── config/
├── couple/
├── anniversaries/
├── photos/albums.json
├── chats/
├── gifts/
├── diary/
└── mood/
```

## 🔴 Redis存储方案

### Redis数据结构设计

#### 1. 键命名规范
```
love:config:app                    # 应用配置
love:couple:profile               # 情侣档案
love:anniversaries:list           # 纪念日列表
love:anniversaries:{id}           # 单个纪念日
love:photos:albums               # 相册列表
love:photos:album:{id}           # 单个相册
love:chats:{year}:{month}        # 聊天记录
love:gifts:list                  # 礼物列表
love:diary:{year}:{month}        # 日记记录
love:mood:records                # 心情记录
love:cache:*                     # 缓存数据
```

#### 2. 数据类型使用

**String类型** - 存储JSON配置
```redis
SET love:config:app '{"version":"1.0.0","settings":{...}}'
SET love:couple:profile '{"partner1":{...},"partner2":{...}}'
```

**Hash类型** - 存储结构化数据
```redis
HSET love:anniversaries:anniversary_uuid title "恋爱一周年"
HSET love:anniversaries:anniversary_uuid date "2021-02-14"
HSET love:anniversaries:anniversary_uuid type "anniversary"
```

**List类型** - 存储有序列表
```redis
LPUSH love:photos:album:album_uuid:photos photo_uuid1
LPUSH love:photos:album:album_uuid:photos photo_uuid2
```

**Set类型** - 存储标签和分类
```redis
SADD love:photos:tags "旅行" "约会" "美食"
SADD love:photos:album:album_uuid:tags "春游" "户外"
```

**Sorted Set类型** - 存储带时间戳的数据
```redis
ZADD love:diary:timeline 1642204800 diary_uuid1
ZADD love:mood:timeline 1642291200 mood_uuid1
```

### Redis连接配置

#### Go Redis客户端配置
```go
package storage

import (
    "github.com/go-redis/redis/v8"
    "context"
    "time"
)

type RedisStorage struct {
    client *redis.Client
    ctx    context.Context
}

func NewRedisStorage(addr, password string, db int) *RedisStorage {
    rdb := redis.NewClient(&redis.Options{
        Addr:         addr,
        Password:     password,
        DB:           db,
        PoolSize:     10,
        MinIdleConns: 5,
        MaxRetries:   3,
        DialTimeout:  5 * time.Second,
        ReadTimeout:  3 * time.Second,
        WriteTimeout: 3 * time.Second,
    })

    return &RedisStorage{
        client: rdb,
        ctx:    context.Background(),
    }
}
```

### 缓存策略

#### 多级缓存架构
```
应用层缓存 (内存)
    ↓
Redis缓存 (可选)
    ↓
本地文件存储 (持久化)
```

#### 缓存键设计
```redis
# 热点数据缓存 (TTL: 1小时)
love:cache:couple:profile          # 情侣档案
love:cache:anniversaries:upcoming  # 即将到来的纪念日

# 统计数据缓存 (TTL: 24小时)
love:cache:stats:photos:count      # 照片总数
love:cache:stats:diary:count       # 日记总数

# 搜索结果缓存 (TTL: 30分钟)
love:cache:search:{query_hash}     # 搜索结果
```

## 🔧 存储接口设计

### 统一存储接口
```go
type Storage interface {
    // 基础操作
    Get(key string) ([]byte, error)
    Set(key string, value []byte) error
    Delete(key string) error
    Exists(key string) (bool, error)
    
    // 列表操作
    GetList(pattern string) ([]string, error)
    
    // 备份操作
    Backup(path string) error
    Restore(path string) error
    
    // 关闭连接
    Close() error
}
```

### 存储工厂模式
```go
type StorageFactory struct{}

func (f *StorageFactory) CreateStorage(config StorageConfig) Storage {
    switch config.Type {
    case "local":
        return NewLocalStorage(config.Path)
    case "redis":
        return NewRedisStorage(config.Addr, config.Password, config.DB)
    default:
        return NewLocalStorage("./data")
    }
}
```

## 📊 性能优化

### 数据分片策略
- **按时间分片**: 聊天记录、日记按月分割
- **按类型分片**: 不同功能模块独立存储
- **按大小分片**: 大文件单独存储，索引分离

### 索引优化
```json
{
  "indexes": {
    "photos": {
      "byDate": "photos/indexes/by_date.json",
      "byTag": "photos/indexes/by_tag.json",
      "byAlbum": "photos/indexes/by_album.json"
    },
    "diary": {
      "byDate": "diary/indexes/by_date.json",
      "byMood": "diary/indexes/by_mood.json"
    }
  }
}
```

### 压缩策略
- **JSON压缩**: 使用gzip压缩大型JSON文件
- **图片压缩**: 自动生成缩略图，原图按需加载
- **备份压缩**: 备份文件使用zip压缩
