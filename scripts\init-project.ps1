# 爱情个人软件 - 项目初始化脚本
# PowerShell脚本，用于快速搭建开发环境

Write-Host "💕 爱情个人软件 - 项目初始化" -ForegroundColor Magenta
Write-Host "================================" -ForegroundColor Magenta

# 检查必要的工具
Write-Host "🔍 检查开发环境..." -ForegroundColor Yellow

# 检查Go版本
try {
    $goVersion = go version
    Write-Host "✅ Go: $goVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Go未安装，请先安装Go 1.21+" -ForegroundColor Red
    exit 1
}

# 检查Node.js版本
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js未安装，请先安装Node.js 16+" -ForegroundColor Red
    exit 1
}

# 检查Wails CLI
try {
    $wailsVersion = wails version
    Write-Host "✅ Wails: $wailsVersion" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Wails CLI未安装，正在安装..." -ForegroundColor Yellow
    go install github.com/wailsapp/wails/v2/cmd/wails@latest
    Write-Host "✅ Wails CLI安装完成" -ForegroundColor Green
}

# 创建项目目录结构
Write-Host "📁 创建项目目录结构..." -ForegroundColor Yellow

$directories = @(
    "backend/models",
    "backend/services", 
    "backend/storage",
    "frontend/src/assets/images",
    "frontend/src/assets/fonts",
    "frontend/src/components",
    "frontend/src/pages",
    "frontend/src/styles",
    "frontend/src/utils",
    "data/config",
    "data/couple",
    "data/anniversaries",
    "data/photos/images/avatars",
    "data/photos/images/thumbnails",
    "data/chats",
    "data/gifts",
    "data/diary",
    "data/mood",
    "data/backup/auto",
    "data/backup/manual",
    "docs",
    "scripts"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "  ✅ 创建目录: $dir" -ForegroundColor Green
    }
}

# 初始化Wails项目
Write-Host "🚀 初始化Wails项目..." -ForegroundColor Yellow

if (!(Test-Path "wails.json")) {
    # 创建wails.json配置文件
    $wailsConfig = @{
        "$schema" = "https://wails.io/schemas/config.v2.json"
        name = "love-software"
        outputfilename = "爱情个人软件"
        frontend = @{
            dir = "./frontend"
            install = "npm install"
            build = "npm run build"
            dev = "npm run dev"
            package = @{
                manager = "npm"
            }
        }
        backend = @{
            dir = "./backend"
        }
        author = @{
            name = "Love Software Developer"
            email = "<EMAIL>"
        }
        info = @{
            companyName = "Love Software"
            productName = "爱情个人软件"
            productVersion = "1.0.0"
            copyright = "Copyright © 2024 Love Software. All rights reserved."
            comments = "基于Go和Wails v2开发的爱情管理软件"
        }
        nsisType = "multiple"
    } | ConvertTo-Json -Depth 10

    $wailsConfig | Out-File -FilePath "wails.json" -Encoding UTF8
    Write-Host "  ✅ 创建wails.json配置文件" -ForegroundColor Green
}

# 创建Go模块
Write-Host "📦 初始化Go模块..." -ForegroundColor Yellow

if (!(Test-Path "go.mod")) {
    go mod init love-software
    Write-Host "  ✅ 创建go.mod文件" -ForegroundColor Green
}

# 安装Go依赖
Write-Host "📥 安装Go依赖..." -ForegroundColor Yellow

$goDependencies = @(
    "github.com/wailsapp/wails/v2@latest",
    "github.com/go-redis/redis/v8",
    "golang.org/x/crypto"
)

foreach ($dep in $goDependencies) {
    Write-Host "  📦 安装: $dep" -ForegroundColor Cyan
    go get $dep
}

# 创建前端package.json
Write-Host "📄 创建前端配置文件..." -ForegroundColor Yellow

$packageJson = @{
    name = "love-software-frontend"
    version = "1.0.0"
    description = "爱情个人软件前端"
    main = "index.html"
    scripts = @{
        dev = "vite"
        build = "vite build"
        preview = "vite preview"
    }
    devDependencies = @{
        vite = "^5.0.0"
        "@vitejs/plugin-legacy" = "^5.0.0"
        "autoprefixer" = "^10.4.16"
        "postcss" = "^8.4.32"
        "tailwindcss" = "^3.3.6"
    }
    dependencies = @{
        "font-awesome" = "^4.7.0"
    }
} | ConvertTo-Json -Depth 10

$packageJson | Out-File -FilePath "frontend/package.json" -Encoding UTF8
Write-Host "  ✅ 创建package.json文件" -ForegroundColor Green

# 创建前端入口文件
$indexHtml = @"
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💕 爱情个人软件</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'PingFang SC', 'Microsoft YaHei', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        'primary-pink': '#ff6b9d',
                        'primary-purple': '#a855f7',
                        'secondary-blue': '#3b82f6',
                        'accent-gold': '#fbbf24',
                    }
                }
            }
        }
    </script>
    <style>
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', 'PingFang SC', 'Microsoft YaHei', system-ui, sans-serif;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="min-h-screen flex items-center justify-center p-8">
            <div class="glass-card p-8 max-w-md w-full text-center">
                <div class="text-6xl mb-4">💕</div>
                <h1 class="text-3xl font-bold text-white mb-4">爱情个人软件</h1>
                <p class="text-white/80 mb-6">基于 Go + Wails v2 开发</p>
                <button class="glass-button px-6 py-3 rounded-lg text-white font-medium w-full">
                    <i class="fas fa-heart mr-2"></i>
                    开始使用
                </button>
            </div>
        </div>
    </div>
    <script type="module" src="/src/main.js"></script>
</body>
</html>
"@

$indexHtml | Out-File -FilePath "frontend/index.html" -Encoding UTF8
Write-Host "  ✅ 创建index.html文件" -ForegroundColor Green

# 创建基础的main.js文件
$mainJs = @"
// 爱情个人软件 - 前端入口文件
console.log('💕 爱情个人软件启动中...')

// 等待Wails运行时准备就绪
window.addEventListener('DOMContentLoaded', () => {
    console.log('✅ 前端界面加载完成')
    
    // 初始化应用
    initApp()
})

function initApp() {
    console.log('🚀 初始化应用...')
    
    // 这里将添加应用初始化逻辑
    // 包括路由、状态管理、组件初始化等
}

// 导出给Wails使用的函数
window.showMessage = function(message) {
    alert(message)
}
"@

$mainJs | Out-File -FilePath "frontend/src/main.js" -Encoding UTF8
Write-Host "  ✅ 创建main.js文件" -ForegroundColor Green

# 创建基础的Go后端文件
Write-Host "🔧 创建Go后端文件..." -ForegroundColor Yellow

$mainGo = @"
package main

import (
	"context"
	"embed"
	"fmt"
	"log"

	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/options/assetserver"
)

//go:embed all:frontend/dist
var assets embed.FS

// App struct
type App struct {
	ctx context.Context
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{}
}

// startup is called when the app starts. The context passed
// is saved so we can call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx
	fmt.Println("💕 爱情个人软件后端启动成功")
}

// Greet returns a greeting for the given name
func (a *App) Greet(name string) string {
	return fmt.Sprintf("Hello %s, It's show time!", name)
}

func main() {
	// Create an instance of the app structure
	app := NewApp()

	// Create application with options
	err := wails.Run(&options.App{
		Title:  "💕 爱情个人软件",
		Width:  1200,
		Height: 800,
		AssetServer: &assetserver.Options{
			Assets: assets,
		},
		BackgroundColour: &options.RGBA{R: 27, G: 38, B: 54, A: 1},
		OnStartup:        app.startup,
		Bind: []interface{}{
			app,
		},
	})

	if err != nil {
		log.Fatal("Error:", err)
	}
}
"@

$mainGo | Out-File -FilePath "main.go" -Encoding UTF8
Write-Host "  ✅ 创建main.go文件" -ForegroundColor Green

# 安装前端依赖
Write-Host "📥 安装前端依赖..." -ForegroundColor Yellow
Set-Location frontend
npm install
Set-Location ..
Write-Host "  ✅ 前端依赖安装完成" -ForegroundColor Green

# 完成提示
Write-Host ""
Write-Host "🎉 项目初始化完成！" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host ""
Write-Host "📋 下一步操作:" -ForegroundColor Yellow
Write-Host "  1. 开发模式运行: wails dev" -ForegroundColor Cyan
Write-Host "  2. 构建生产版本: wails build" -ForegroundColor Cyan
Write-Host "  3. 查看项目文档: docs/" -ForegroundColor Cyan
Write-Host ""
Write-Host "📁 项目结构已创建完成，包含:" -ForegroundColor Yellow
Write-Host "  ✅ Wails配置文件" -ForegroundColor Green
Write-Host "  ✅ Go后端基础结构" -ForegroundColor Green  
Write-Host "  ✅ 前端基础文件" -ForegroundColor Green
Write-Host "  ✅ 数据存储目录" -ForegroundColor Green
Write-Host "  ✅ 完整项目文档" -ForegroundColor Green
Write-Host ""
Write-Host "💕 开始你的爱情软件开发之旅吧！" -ForegroundColor Magenta
"@
