# 🔌 API接口文档 - API Documentation

## 🎯 接口概述

基于Wails v2框架，前端通过JavaScript调用Go后端方法，实现数据交互。

## 🏗️ 接口架构

### Wails绑定方式
```go
// main.go
func main() {
    app := NewApp()
    
    err := wails.Run(&options.App{
        Title:  "爱情个人软件",
        Width:  1200,
        Height: 800,
        AssetServer: &assetserver.Options{
            Assets: assets,
        },
        BackgroundColour: &options.RGBA{R: 27, G: 38, B: 54, A: 1},
        OnStartup:        app.startup,
        Bind: []interface{}{
            app,
        },
    })
}
```

### 前端调用方式
```javascript
// 前端调用Go方法
import { GetCoupleProfile, UpdateCoupleProfile } from '../wailsjs/go/main/App'

// 获取情侣档案
const profile = await GetCoupleProfile()

// 更新情侣档案
await UpdateCoupleProfile(profileData)
```

## 📋 接口列表

### 1. 情侣档案管理

#### 获取情侣档案
```go
// GetCoupleProfile 获取情侣档案信息
func (a *App) GetCoupleProfile(ctx context.Context) (*models.CoupleProfile, error) {
    return a.storage.GetCoupleProfile()
}
```

**前端调用**:
```javascript
const profile = await GetCoupleProfile()
```

**返回数据**:
```json
{
  "id": "couple_uuid",
  "partner1": {
    "name": "张三",
    "nickname": "小三",
    "birthday": "1995-05-20",
    "avatar": "images/avatars/partner1.jpg"
  },
  "partner2": {
    "name": "李四",
    "nickname": "小四", 
    "birthday": "1996-08-15",
    "avatar": "images/avatars/partner2.jpg"
  },
  "relationship": {
    "startDate": "2020-02-14",
    "status": "恋爱中"
  }
}
```

#### 更新情侣档案
```go
// UpdateCoupleProfile 更新情侣档案
func (a *App) UpdateCoupleProfile(ctx context.Context, profile *models.CoupleProfile) error {
    return a.storage.UpdateCoupleProfile(profile)
}
```

**前端调用**:
```javascript
await UpdateCoupleProfile({
  partner1: { name: "新名字", ... },
  partner2: { name: "新名字", ... }
})
```

### 2. 纪念日管理

#### 获取纪念日列表
```go
// GetAnniversaries 获取纪念日列表
func (a *App) GetAnniversaries(ctx context.Context) ([]*models.Anniversary, error) {
    return a.storage.GetAnniversaries()
}
```

#### 添加纪念日
```go
// AddAnniversary 添加新纪念日
func (a *App) AddAnniversary(ctx context.Context, anniversary *models.Anniversary) error {
    anniversary.ID = generateUUID()
    anniversary.CreatedAt = time.Now()
    return a.storage.AddAnniversary(anniversary)
}
```

#### 更新纪念日
```go
// UpdateAnniversary 更新纪念日
func (a *App) UpdateAnniversary(ctx context.Context, anniversary *models.Anniversary) error {
    anniversary.LastModified = time.Now()
    return a.storage.UpdateAnniversary(anniversary)
}
```

#### 删除纪念日
```go
// DeleteAnniversary 删除纪念日
func (a *App) DeleteAnniversary(ctx context.Context, id string) error {
    return a.storage.DeleteAnniversary(id)
}
```

### 3. 照片相册管理

#### 获取相册列表
```go
// GetAlbums 获取相册列表
func (a *App) GetAlbums(ctx context.Context) ([]*models.Album, error) {
    return a.storage.GetAlbums()
}
```

#### 创建相册
```go
// CreateAlbum 创建新相册
func (a *App) CreateAlbum(ctx context.Context, album *models.Album) error {
    album.ID = generateUUID()
    album.CreatedAt = time.Now()
    return a.storage.CreateAlbum(album)
}
```

#### 上传照片
```go
// UploadPhoto 上传照片到相册
func (a *App) UploadPhoto(ctx context.Context, albumID string, photoData []byte, filename string) (*models.Photo, error) {
    // 保存原图
    photoPath := filepath.Join("data/photos/images", generatePhotoPath(filename))
    err := os.WriteFile(photoPath, photoData, 0644)
    if err != nil {
        return nil, err
    }
    
    // 生成缩略图
    thumbnailPath, err := generateThumbnail(photoPath)
    if err != nil {
        return nil, err
    }
    
    photo := &models.Photo{
        ID:            generateUUID(),
        Filename:      filename,
        OriginalPath:  photoPath,
        ThumbnailPath: thumbnailPath,
        UploadedAt:    time.Now(),
        FileSize:      int64(len(photoData)),
    }
    
    return photo, a.storage.AddPhotoToAlbum(albumID, photo)
}
```

#### 获取照片
```go
// GetPhoto 获取照片数据
func (a *App) GetPhoto(ctx context.Context, photoPath string) ([]byte, error) {
    return os.ReadFile(photoPath)
}
```

### 4. 聊天记录管理

#### 获取聊天记录
```go
// GetChatRecords 获取指定月份的聊天记录
func (a *App) GetChatRecords(ctx context.Context, year int, month int) ([]*models.ChatRecord, error) {
    return a.storage.GetChatRecords(year, month)
}
```

#### 添加聊天记录
```go
// AddChatMessage 添加聊天消息
func (a *App) AddChatMessage(ctx context.Context, message *models.ChatMessage) error {
    message.ID = generateUUID()
    message.Timestamp = time.Now()
    return a.storage.AddChatMessage(message)
}
```

### 5. 礼物管理

#### 获取礼物列表
```go
// GetGifts 获取礼物列表
func (a *App) GetGifts(ctx context.Context) ([]*models.Gift, error) {
    return a.storage.GetGifts()
}
```

#### 添加礼物记录
```go
// AddGift 添加礼物记录
func (a *App) AddGift(ctx context.Context, gift *models.Gift) error {
    gift.ID = generateUUID()
    gift.CreatedAt = time.Now()
    return a.storage.AddGift(gift)
}
```

### 6. 日记管理

#### 获取日记列表
```go
// GetDiaryEntries 获取指定月份的日记
func (a *App) GetDiaryEntries(ctx context.Context, year int, month int) ([]*models.DiaryEntry, error) {
    return a.storage.GetDiaryEntries(year, month)
}
```

#### 保存日记
```go
// SaveDiaryEntry 保存日记条目
func (a *App) SaveDiaryEntry(ctx context.Context, entry *models.DiaryEntry) error {
    if entry.ID == "" {
        entry.ID = generateUUID()
        entry.CreatedAt = time.Now()
    }
    entry.LastModified = time.Now()
    return a.storage.SaveDiaryEntry(entry)
}
```

### 7. 心情记录

#### 获取心情记录
```go
// GetMoodRecords 获取心情记录
func (a *App) GetMoodRecords(ctx context.Context, limit int) ([]*models.MoodRecord, error) {
    return a.storage.GetMoodRecords(limit)
}
```

#### 添加心情记录
```go
// AddMoodRecord 添加心情记录
func (a *App) AddMoodRecord(ctx context.Context, mood *models.MoodRecord) error {
    mood.ID = generateUUID()
    mood.CreatedAt = time.Now()
    return a.storage.AddMoodRecord(mood)
}
```

### 8. 系统功能

#### 获取应用配置
```go
// GetAppConfig 获取应用配置
func (a *App) GetAppConfig(ctx context.Context) (*models.AppConfig, error) {
    return a.storage.GetAppConfig()
}
```

#### 更新应用配置
```go
// UpdateAppConfig 更新应用配置
func (a *App) UpdateAppConfig(ctx context.Context, config *models.AppConfig) error {
    return a.storage.UpdateAppConfig(config)
}
```

#### 数据备份
```go
// BackupData 备份数据
func (a *App) BackupData(ctx context.Context, backupPath string) error {
    return a.storage.BackupData(backupPath)
}
```

#### 数据恢复
```go
// RestoreData 恢复数据
func (a *App) RestoreData(ctx context.Context, backupPath string) error {
    return a.storage.RestoreData(backupPath)
}
```

#### 选择文件对话框
```go
// SelectFile 打开文件选择对话框
func (a *App) SelectFile(ctx context.Context, filters []string) (string, error) {
    selection, err := runtime.OpenFileDialog(ctx, runtime.OpenDialogOptions{
        Title:   "选择文件",
        Filters: parseFilters(filters),
    })
    return selection, err
}
```

#### 选择文件夹对话框
```go
// SelectDirectory 打开文件夹选择对话框
func (a *App) SelectDirectory(ctx context.Context) (string, error) {
    selection, err := runtime.OpenDirectoryDialog(ctx, runtime.OpenDialogOptions{
        Title: "选择文件夹",
    })
    return selection, err
}
```

## 🔧 数据模型定义

### 基础模型
```go
// models/base.go
type BaseModel struct {
    ID           string    `json:"id"`
    CreatedAt    time.Time `json:"createdAt"`
    LastModified time.Time `json:"lastModified,omitempty"`
}
```

### 情侣档案模型
```go
// models/couple.go
type CoupleProfile struct {
    BaseModel
    Partner1     *Partner     `json:"partner1"`
    Partner2     *Partner     `json:"partner2"`
    Relationship *Relationship `json:"relationship"`
}

type Partner struct {
    ID          string   `json:"id"`
    Name        string   `json:"name"`
    Nickname    string   `json:"nickname"`
    Birthday    string   `json:"birthday"`
    Avatar      string   `json:"avatar"`
    Preferences []string `json:"preferences"`
}

type Relationship struct {
    StartDate  string      `json:"startDate"`
    Status     string      `json:"status"`
    Milestones []Milestone `json:"milestones"`
}
```

### 纪念日模型
```go
// models/anniversary.go
type Anniversary struct {
    BaseModel
    Title        string   `json:"title"`
    Date         string   `json:"date"`
    Type         string   `json:"type"`
    Recurring    string   `json:"recurring"`
    ReminderDays []int    `json:"reminderDays"`
    Description  string   `json:"description"`
    IsActive     bool     `json:"isActive"`
}
```

## 🚨 错误处理

### 统一错误响应
```go
type APIError struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}

func (e *APIError) Error() string {
    return e.Message
}
```

### 错误码定义
```go
const (
    ErrCodeSuccess         = 0
    ErrCodeInvalidParam    = 1001
    ErrCodeNotFound        = 1002
    ErrCodeStorageError    = 1003
    ErrCodePermissionDenied = 1004
    ErrCodeInternalError   = 1005
)
```

## 🔐 安全考虑

### 数据验证
```go
func validateCoupleProfile(profile *models.CoupleProfile) error {
    if profile.Partner1 == nil || profile.Partner2 == nil {
        return errors.New("partner information is required")
    }
    
    if profile.Partner1.Name == "" || profile.Partner2.Name == "" {
        return errors.New("partner name is required")
    }
    
    return nil
}
```

### 文件路径安全
```go
func sanitizeFilePath(path string) string {
    // 防止路径遍历攻击
    path = filepath.Clean(path)
    path = strings.ReplaceAll(path, "..", "")
    return path
}
```
