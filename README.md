# 💕 爱情个人软件 - Love Personal Software

基于 Go + Wails v2 开发的桌面端爱情管理软件，采用现代化 Glassmorphism 设计风格。

## 🎯 项目概述

这是一款专为情侣设计的个人桌面软件，帮助记录和管理爱情生活中的美好时光。

### 核心特性
- 💑 情侣档案管理
- 📅 纪念日提醒
- 📸 照片相册管理
- 💬 聊天记录保存
- 🎁 礼物清单管理
- 📝 爱情日记
- 🌟 心情记录

## 🏗️ 技术架构

### 前端技术栈
- **HTML5** - 现代化标记语言
- **CSS3** - 样式设计，支持 Glassmorphism 效果
- **JavaScript (ES6+)** - 现代化交互逻辑
- **Tailwind CSS** - 实用优先的 CSS 框架
- **Font Awesome 6.4.0** - 图标库
- **Inter Font Family** - 现代化字体

### 后端技术栈
- **Go 1.21+** - 高性能后端语言
- **Wails v2** - Go + Web 桌面应用框架
- **JSON** - 本地数据存储格式
- **Redis** - 可选的数据存储方案

### 桌面框架
- **Wails v2** - 跨平台桌面应用框架
- **WebView2** (Windows) - 现代化 Web 渲染引擎
- **WebKit** (Linux) - Linux 平台 Web 引擎
- **WKWebView** (macOS) - macOS 平台 Web 引擎

## 📁 项目结构

```
爱情电脑软件/
├── app/                    # Wails 应用配置
├── backend/               # Go 后端代码
│   ├── models/           # 数据模型
│   ├── services/         # 业务逻辑
│   ├── storage/          # 存储层
│   └── main.go          # 主程序入口
├── frontend/             # 前端代码
│   ├── dist/            # 构建输出
│   ├── src/             # 源代码
│   │   ├── assets/      # 静态资源
│   │   ├── components/  # 组件
│   │   ├── pages/       # 页面
│   │   ├── styles/      # 样式文件
│   │   └── utils/       # 工具函数
│   ├── index.html       # 主页面
│   └── package.json     # 前端依赖
├── data/                 # 本地数据存储
├── docs/                 # 项目文档
├── wails.json           # Wails 配置文件
├── go.mod               # Go 模块文件
└── README.md            # 项目说明
```

## 🎨 UI 设计规范

### Glassmorphism 设计风格
- **毛玻璃效果**: backdrop-filter: blur(10px)
- **半透明背景**: rgba(255, 255, 255, 0.1)
- **柔和边框**: border: 1px solid rgba(255, 255, 255, 0.2)
- **渐变色彩**: 粉色到紫色的爱情主题渐变

### 色彩方案
```css
:root {
  --primary-pink: #ff6b9d;
  --primary-purple: #a855f7;
  --secondary-blue: #3b82f6;
  --accent-gold: #fbbf24;
  --glass-white: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
}
```

### 字体规范
- **主字体**: Inter, system-ui, sans-serif
- **标题字重**: 600-700
- **正文字重**: 400-500
- **字号层级**: 12px, 14px, 16px, 18px, 24px, 32px

## 🚀 快速开始

### 环境要求
- Go 1.21+
- Node.js 16+
- Wails v2 CLI

### 安装步骤
```bash
# 1. 安装 Wails CLI
go install github.com/wailsapp/wails/v2/cmd/wails@latest

# 2. 创建项目
wails init -n love-software -t vanilla

# 3. 安装前端依赖
cd frontend && npm install

# 4. 开发模式运行
wails dev

# 5. 构建生产版本
wails build
```

## 📊 开发进度

详见 [开发进度文档](docs/progress.md)

## 📖 详细文档

- [功能设计文档](docs/features.md)
- [UI设计文档](docs/ui-design.md)
- [数据存储文档](docs/data-storage.md)
- [API接口文档](docs/api.md)

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 📄 许可证

MIT License
