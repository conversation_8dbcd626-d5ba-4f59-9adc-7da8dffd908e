# 💕 功能设计文档 - Features Design

## 🎯 核心功能模块

### 1. 💑 情侣档案管理
**功能描述**: 管理情侣双方的基本信息和关系状态

#### 主要功能
- **个人信息管理**
  - 姓名、昵称、生日
  - 头像上传和管理
  - 个人喜好记录
  - 联系方式管理

- **关系信息**
  - 恋爱开始日期
  - 关系状态 (恋爱中/订婚/已婚)
  - 关系里程碑记录
  - 共同目标设定

#### 数据结构
```json
{
  "couple": {
    "id": "uuid",
    "createdAt": "2024-01-01T00:00:00Z",
    "partner1": {
      "name": "张三",
      "nickname": "小三",
      "birthday": "1995-05-20",
      "avatar": "path/to/avatar1.jpg",
      "preferences": ["电影", "旅行", "美食"]
    },
    "partner2": {
      "name": "李四",
      "nickname": "小四",
      "birthday": "1996-08-15",
      "avatar": "path/to/avatar2.jpg",
      "preferences": ["音乐", "读书", "运动"]
    },
    "relationship": {
      "startDate": "2020-02-14",
      "status": "恋爱中",
      "milestones": [
        {
          "date": "2020-02-14",
          "event": "第一次约会",
          "description": "在咖啡厅的美好相遇"
        }
      ]
    }
  }
}
```

### 2. 📅 纪念日管理
**功能描述**: 记录和提醒重要的纪念日

#### 主要功能
- **纪念日类型**
  - 恋爱纪念日 (每月/每年)
  - 生日提醒
  - 特殊节日 (情人节、七夕等)
  - 自定义纪念日

- **提醒功能**
  - 桌面通知提醒
  - 倒计时显示
  - 提前提醒设置
  - 重复提醒配置

#### 数据结构
```json
{
  "anniversaries": [
    {
      "id": "uuid",
      "title": "恋爱一周年",
      "date": "2021-02-14",
      "type": "anniversary",
      "recurring": "yearly",
      "reminderDays": [7, 3, 1],
      "description": "我们在一起的第一个年头",
      "isActive": true
    }
  ]
}
```

### 3. 📸 照片相册管理
**功能描述**: 管理情侣间的珍贵照片和回忆

#### 主要功能
- **相册分类**
  - 按时间分类 (年/月)
  - 按事件分类 (旅行/约会/节日)
  - 按地点分类
  - 自定义标签分类

- **照片管理**
  - 批量上传和导入
  - 照片标签和描述
  - 照片搜索和筛选
  - 幻灯片播放

#### 数据结构
```json
{
  "albums": [
    {
      "id": "uuid",
      "name": "2024年春游",
      "coverPhoto": "path/to/cover.jpg",
      "createdAt": "2024-03-15T00:00:00Z",
      "tags": ["旅行", "春天", "户外"],
      "photos": [
        {
          "id": "uuid",
          "filename": "IMG_001.jpg",
          "path": "photos/2024/03/IMG_001.jpg",
          "takenAt": "2024-03-15T14:30:00Z",
          "description": "在樱花树下的合影",
          "tags": ["樱花", "合影"]
        }
      ]
    }
  ]
}
```

### 4. 💬 聊天记录保存
**功能描述**: 保存和管理重要的聊天记录

#### 主要功能
- **记录管理**
  - 手动添加聊天记录
  - 按日期分类查看
  - 关键词搜索
  - 收藏重要对话

- **内容类型**
  - 文字消息
  - 图片消息
  - 语音消息 (文字描述)
  - 表情包记录

#### 数据结构
```json
{
  "chatRecords": [
    {
      "id": "uuid",
      "date": "2024-01-15",
      "messages": [
        {
          "id": "uuid",
          "timestamp": "2024-01-15T20:30:00Z",
          "sender": "partner1",
          "type": "text",
          "content": "今天的约会真开心！",
          "isStarred": true
        }
      ]
    }
  ]
}
```

### 5. 🎁 礼物清单管理
**功能描述**: 记录送出和收到的礼物

#### 主要功能
- **礼物记录**
  - 礼物名称和描述
  - 送礼日期和场合
  - 礼物照片
  - 价格记录 (可选)

- **愿望清单**
  - 想要的礼物列表
  - 优先级设置
  - 价格预算
  - 购买状态跟踪

### 6. 📝 爱情日记
**功能描述**: 记录爱情生活中的点点滴滴

#### 主要功能
- **日记写作**
  - 富文本编辑器
  - 插入照片和表情
  - 心情标签
  - 天气记录

- **日记管理**
  - 按日期浏览
  - 标签分类
  - 搜索功能
  - 导出功能

### 7. 🌟 心情记录
**功能描述**: 记录每日心情和感受

#### 主要功能
- **心情追踪**
  - 心情评分 (1-10分)
  - 心情标签 (开心/难过/兴奋等)
  - 心情原因记录
  - 心情统计图表

#### 数据结构
```json
{
  "moodRecords": [
    {
      "id": "uuid",
      "date": "2024-01-15",
      "score": 8,
      "mood": "开心",
      "reason": "和TA一起看了电影",
      "tags": ["约会", "电影", "开心"],
      "note": "今天心情特别好"
    }
  ]
}
```

## 🎨 用户界面设计

### 主界面布局
```
┌─────────────────────────────────────────┐
│  💕 爱情个人软件                          │
├─────────────────────────────────────────┤
│ 🏠 首页  📅 纪念日  📸 相册  💬 聊天     │
├─────────────────────────────────────────┤
│                                         │
│         主要内容区域                     │
│                                         │
│                                         │
├─────────────────────────────────────────┤
│ 🎁 礼物  📝 日记  🌟 心情  ⚙️ 设置      │
└─────────────────────────────────────────┘
```

### Glassmorphism 效果实现
```css
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-button {
  background: linear-gradient(135deg, 
    rgba(255, 107, 157, 0.3), 
    rgba(168, 85, 247, 0.3));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.glass-button:hover {
  background: linear-gradient(135deg, 
    rgba(255, 107, 157, 0.5), 
    rgba(168, 85, 247, 0.5));
  transform: translateY(-2px);
}
```

## 🔧 技术实现要点

### 数据存储策略
1. **本地JSON存储**
   - 轻量级，无需额外依赖
   - 适合个人使用
   - 数据加密保护隐私

2. **Redis存储 (可选)**
   - 高性能数据访问
   - 支持数据缓存
   - 适合频繁读写操作

### 安全性考虑
- 本地数据加密存储
- 敏感信息脱敏处理
- 数据备份和恢复机制
- 用户隐私保护

### 性能优化
- 图片懒加载和压缩
- 数据分页加载
- 缓存机制优化
- 内存使用优化
