# 🎨 UI设计文档 - User Interface Design

## 🌟 设计理念

### Glassmorphism 风格
采用现代化的毛玻璃拟态设计风格，营造浪漫、温馨的爱情主题氛围。

#### 核心设计原则
- **透明度与层次**: 利用半透明效果创造深度感
- **柔和边界**: 圆角设计增加亲和力
- **渐变色彩**: 粉色到紫色的爱情主题渐变
- **微妙阴影**: 增强立体感和层次感

## 🎨 色彩系统

### 主色调
```css
:root {
  /* 主要色彩 */
  --primary-pink: #ff6b9d;        /* 主粉色 */
  --primary-purple: #a855f7;      /* 主紫色 */
  --secondary-blue: #3b82f6;      /* 辅助蓝色 */
  --accent-gold: #fbbf24;         /* 强调金色 */
  
  /* 玻璃效果色彩 */
  --glass-white: rgba(255, 255, 255, 0.1);
  --glass-white-hover: rgba(255, 255, 255, 0.2);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: rgba(0, 0, 0, 0.1);
  
  /* 渐变色彩 */
  --gradient-primary: linear-gradient(135deg, #ff6b9d, #a855f7);
  --gradient-secondary: linear-gradient(135deg, #3b82f6, #8b5cf6);
  --gradient-accent: linear-gradient(135deg, #fbbf24, #f59e0b);
  
  /* 文字色彩 */
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-light: #9ca3af;
  --text-white: #ffffff;
}
```

### 色彩使用规范
- **主色调**: 用于重要按钮、标题、强调元素
- **辅助色**: 用于次要按钮、图标、装饰元素
- **中性色**: 用于文字、边框、背景

## 📝 字体系统

### 字体族
```css
:root {
  --font-primary: 'Inter', 'PingFang SC', 'Microsoft YaHei', system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', 'Consolas', monospace;
}
```

### 字体层级
```css
/* 标题字体 */
.text-4xl { font-size: 2.25rem; font-weight: 700; } /* 36px - 主标题 */
.text-3xl { font-size: 1.875rem; font-weight: 600; } /* 30px - 二级标题 */
.text-2xl { font-size: 1.5rem; font-weight: 600; }   /* 24px - 三级标题 */
.text-xl  { font-size: 1.25rem; font-weight: 500; }  /* 20px - 四级标题 */

/* 正文字体 */
.text-lg  { font-size: 1.125rem; font-weight: 400; } /* 18px - 大正文 */
.text-base { font-size: 1rem; font-weight: 400; }    /* 16px - 标准正文 */
.text-sm  { font-size: 0.875rem; font-weight: 400; } /* 14px - 小正文 */
.text-xs  { font-size: 0.75rem; font-weight: 400; }  /* 12px - 辅助文字 */
```

## 🧩 组件设计

### 1. 玻璃卡片组件
```css
.glass-card {
  background: var(--glass-white);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  box-shadow: 0 8px 32px var(--glass-shadow);
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: var(--glass-white-hover);
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}
```

### 2. 玻璃按钮组件
```css
.glass-button {
  background: var(--glass-white);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 12px 24px;
  color: var(--text-primary);
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.glass-button:hover {
  background: var(--glass-white-hover);
  transform: translateY(-2px);
}

.glass-button.primary {
  background: var(--gradient-primary);
  color: var(--text-white);
  border: 1px solid rgba(255, 255, 255, 0.3);
}
```

### 3. 输入框组件
```css
.glass-input {
  background: var(--glass-white);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 12px 16px;
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.glass-input:focus {
  outline: none;
  border-color: var(--primary-pink);
  box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
}
```

### 4. 导航栏组件
```css
.glass-navbar {
  background: var(--glass-white);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--glass-border);
  padding: 16px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-item {
  padding: 8px 16px;
  border-radius: 8px;
  color: var(--text-secondary);
  transition: all 0.3s ease;
  cursor: pointer;
}

.nav-item:hover,
.nav-item.active {
  background: var(--glass-white-hover);
  color: var(--primary-pink);
}
```

## 📱 页面布局设计

### 主界面布局
```html
<div class="app-container">
  <!-- 顶部导航栏 -->
  <header class="glass-navbar">
    <div class="logo">💕 爱情软件</div>
    <nav class="nav-menu">
      <div class="nav-item active">🏠 首页</div>
      <div class="nav-item">📅 纪念日</div>
      <div class="nav-item">📸 相册</div>
      <div class="nav-item">💬 聊天</div>
    </nav>
    <div class="user-menu">⚙️</div>
  </header>
  
  <!-- 主内容区 -->
  <main class="main-content">
    <div class="content-grid">
      <!-- 内容区域 -->
    </div>
  </main>
  
  <!-- 底部工具栏 -->
  <footer class="glass-toolbar">
    <div class="tool-item">🎁 礼物</div>
    <div class="tool-item">📝 日记</div>
    <div class="tool-item">🌟 心情</div>
    <div class="tool-item">⚙️ 设置</div>
  </footer>
</div>
```

### 响应式设计
```css
/* 桌面端 (>= 1024px) */
@media (min-width: 1024px) {
  .app-container {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 24px;
  }
}

/* 平板端 (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }
}

/* 移动端 (< 768px) */
@media (max-width: 767px) {
  .content-grid {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
}
```

## 🎭 动画效果

### 页面切换动画
```css
.page-transition {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-enter {
  opacity: 0;
  transform: translateX(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateX(0);
}

.page-exit {
  opacity: 1;
  transform: translateX(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateX(-20px);
}
```

### 悬浮动画
```css
.hover-float {
  transition: transform 0.3s ease;
}

.hover-float:hover {
  transform: translateY(-4px);
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}
```

### 加载动画
```css
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--glass-border);
  border-top: 3px solid var(--primary-pink);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
```

## 🖼️ 图标系统

### Font Awesome 图标使用
```html
<!-- 功能图标 -->
<i class="fas fa-heart"></i>          <!-- 爱心 -->
<i class="fas fa-calendar-heart"></i>  <!-- 纪念日 -->
<i class="fas fa-images"></i>          <!-- 相册 -->
<i class="fas fa-comments"></i>        <!-- 聊天 -->
<i class="fas fa-gift"></i>            <!-- 礼物 -->
<i class="fas fa-pen-fancy"></i>       <!-- 日记 -->
<i class="fas fa-smile"></i>           <!-- 心情 -->

<!-- 操作图标 -->
<i class="fas fa-plus"></i>            <!-- 添加 -->
<i class="fas fa-edit"></i>            <!-- 编辑 -->
<i class="fas fa-trash"></i>           <!-- 删除 -->
<i class="fas fa-search"></i>          <!-- 搜索 -->
<i class="fas fa-filter"></i>          <!-- 筛选 -->
<i class="fas fa-download"></i>        <!-- 下载 -->
```

### 自定义图标样式
```css
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.icon.primary {
  color: var(--primary-pink);
}

.icon.large {
  width: 32px;
  height: 32px;
  font-size: 1.5rem;
}

.icon.small {
  width: 16px;
  height: 16px;
  font-size: 0.875rem;
}
```

## 📐 间距系统

### 标准间距
```css
:root {
  --space-1: 0.25rem;  /* 4px */
  --space-2: 0.5rem;   /* 8px */
  --space-3: 0.75rem;  /* 12px */
  --space-4: 1rem;     /* 16px */
  --space-5: 1.25rem;  /* 20px */
  --space-6: 1.5rem;   /* 24px */
  --space-8: 2rem;     /* 32px */
  --space-10: 2.5rem;  /* 40px */
  --space-12: 3rem;    /* 48px */
  --space-16: 4rem;    /* 64px */
}
```

### 使用规范
- **组件内间距**: 使用 space-2 到 space-4
- **组件间间距**: 使用 space-4 到 space-6
- **区块间间距**: 使用 space-8 到 space-12
- **页面边距**: 使用 space-6 到 space-8
