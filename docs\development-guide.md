# 🛠️ 开发指南 - Development Guide

## 🚀 快速开始

### 环境准备
1. **Go 1.21+** - [下载安装](https://golang.org/dl/)
2. **Node.js 16+** - [下载安装](https://nodejs.org/)
3. **Git** - [下载安装](https://git-scm.com/)

### 项目初始化

#### Windows用户
```powershell
# 运行PowerShell初始化脚本
.\scripts\init-project.ps1
```

#### Linux/macOS用户
```bash
# 运行Bash初始化脚本
./scripts/init-project.sh
```

#### 手动初始化
```bash
# 1. 安装Wails CLI
go install github.com/wailsapp/wails/v2/cmd/wails@latest

# 2. 初始化Go模块
go mod init love-software

# 3. 安装Go依赖
go get github.com/wailsapp/wails/v2@latest
go get github.com/go-redis/redis/v8
go get golang.org/x/crypto

# 4. 安装前端依赖
cd frontend && npm install && cd ..
```

## 🏃‍♂️ 运行项目

### 开发模式
```bash
# 启动开发服务器（热重载）
wails dev
```

### 构建生产版本
```bash
# 构建可执行文件
wails build

# 构建时指定输出目录
wails build -o ./dist/

# 构建时启用压缩
wails build -upx
```

## 📁 项目结构详解

```
爱情电脑软件/
├── 📄 README.md                 # 项目说明
├── 📄 wails.json               # Wails配置文件
├── 📄 go.mod                   # Go模块文件
├── 📄 main.go                  # Go主程序入口
├── 📁 backend/                 # Go后端代码
│   ├── 📁 models/             # 数据模型
│   │   ├── base.go           # 基础模型
│   │   ├── couple.go         # 情侣档案模型
│   │   ├── anniversary.go    # 纪念日模型
│   │   └── ...
│   ├── 📁 services/           # 业务逻辑层
│   │   ├── couple_service.go # 情侣档案服务
│   │   ├── photo_service.go  # 照片服务
│   │   └── ...
│   └── 📁 storage/            # 数据存储层
│       ├── local_storage.go  # 本地存储实现
│       ├── redis_storage.go  # Redis存储实现
│       └── interface.go      # 存储接口定义
├── 📁 frontend/               # 前端代码
│   ├── 📄 index.html         # 主页面
│   ├── 📄 package.json       # 前端依赖配置
│   ├── 📁 src/               # 源代码
│   │   ├── 📄 main.js        # 前端入口文件
│   │   ├── 📁 components/    # Vue/React组件
│   │   ├── 📁 pages/         # 页面组件
│   │   ├── 📁 styles/        # 样式文件
│   │   ├── 📁 utils/         # 工具函数
│   │   └── 📁 assets/        # 静态资源
│   └── 📁 dist/              # 构建输出目录
├── 📁 data/                   # 本地数据存储
│   ├── 📁 config/            # 配置文件
│   ├── 📁 couple/            # 情侣档案数据
│   ├── 📁 photos/            # 照片数据
│   └── 📁 backup/            # 备份数据
├── 📁 docs/                   # 项目文档
│   ├── features.md           # 功能设计文档
│   ├── ui-design.md          # UI设计文档
│   ├── data-storage.md       # 数据存储文档
│   ├── api.md                # API接口文档
│   └── progress.md           # 开发进度文档
└── 📁 scripts/               # 脚本文件
    ├── init-project.ps1      # Windows初始化脚本
    └── init-project.sh       # Linux/macOS初始化脚本
```

## 🔧 开发工作流

### 1. 功能开发流程
```mermaid
graph LR
    A[需求分析] --> B[设计API接口]
    B --> C[实现Go后端]
    C --> D[开发前端界面]
    D --> E[联调测试]
    E --> F[功能完成]
```

### 2. 代码提交规范
```bash
# 提交格式
git commit -m "type(scope): description"

# 类型说明
feat:     新功能
fix:      修复bug
docs:     文档更新
style:    代码格式调整
refactor: 代码重构
test:     测试相关
chore:    构建过程或辅助工具的变动

# 示例
git commit -m "feat(couple): 添加情侣档案管理功能"
git commit -m "fix(photo): 修复照片上传失败问题"
git commit -m "docs(api): 更新API接口文档"
```

## 🎨 前端开发

### 技术栈
- **HTML5** - 语义化标记
- **CSS3** - 现代样式，支持Glassmorphism
- **JavaScript ES6+** - 现代化JavaScript
- **Tailwind CSS** - 实用优先的CSS框架
- **Font Awesome** - 图标库

### 组件开发规范
```javascript
// 组件文件命名：PascalCase
// 文件：components/CoupleProfile.js

class CoupleProfile {
    constructor(container) {
        this.container = container
        this.data = null
        this.init()
    }
    
    async init() {
        await this.loadData()
        this.render()
        this.bindEvents()
    }
    
    async loadData() {
        // 调用Wails API获取数据
        this.data = await GetCoupleProfile()
    }
    
    render() {
        // 渲染组件HTML
        this.container.innerHTML = this.getTemplate()
    }
    
    getTemplate() {
        return `
            <div class="glass-card p-6">
                <h2 class="text-2xl font-bold mb-4">情侣档案</h2>
                <!-- 组件内容 -->
            </div>
        `
    }
    
    bindEvents() {
        // 绑定事件处理器
    }
}
```

### 样式开发规范
```css
/* 使用CSS自定义属性 */
:root {
    --primary-color: #ff6b9d;
    --glass-bg: rgba(255, 255, 255, 0.1);
}

/* 组件样式命名：BEM规范 */
.couple-profile {
    /* 组件根样式 */
}

.couple-profile__header {
    /* 组件元素样式 */
}

.couple-profile__header--active {
    /* 组件修饰符样式 */
}

/* 玻璃拟态效果 */
.glass-effect {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
}
```

## 🔨 后端开发

### Go代码规范
```go
// 包命名：小写，简洁
package models

// 结构体命名：PascalCase
type CoupleProfile struct {
    ID        string    `json:"id"`
    CreatedAt time.Time `json:"createdAt"`
    // 字段注释
    Partner1  *Partner  `json:"partner1"`  // 伴侣1信息
    Partner2  *Partner  `json:"partner2"`  // 伴侣2信息
}

// 方法命名：PascalCase
func (c *CoupleProfile) Validate() error {
    if c.Partner1 == nil || c.Partner2 == nil {
        return errors.New("partner information is required")
    }
    return nil
}

// 接口命名：以er结尾
type CoupleStorage interface {
    GetCoupleProfile() (*CoupleProfile, error)
    UpdateCoupleProfile(*CoupleProfile) error
}
```

### API开发规范
```go
// API方法必须接收context.Context作为第一个参数
func (a *App) GetCoupleProfile(ctx context.Context) (*models.CoupleProfile, error) {
    // 1. 参数验证
    // 2. 业务逻辑处理
    // 3. 返回结果
    return a.storage.GetCoupleProfile()
}

// 错误处理
func (a *App) UpdateCoupleProfile(ctx context.Context, profile *models.CoupleProfile) error {
    // 验证数据
    if err := profile.Validate(); err != nil {
        return fmt.Errorf("validation failed: %w", err)
    }
    
    // 执行更新
    if err := a.storage.UpdateCoupleProfile(profile); err != nil {
        return fmt.Errorf("update failed: %w", err)
    }
    
    return nil
}
```

## 🧪 测试

### 单元测试
```go
// 测试文件命名：*_test.go
package models

import (
    "testing"
    "github.com/stretchr/testify/assert"
)

func TestCoupleProfile_Validate(t *testing.T) {
    tests := []struct {
        name    string
        profile *CoupleProfile
        wantErr bool
    }{
        {
            name: "valid profile",
            profile: &CoupleProfile{
                Partner1: &Partner{Name: "张三"},
                Partner2: &Partner{Name: "李四"},
            },
            wantErr: false,
        },
        {
            name: "missing partner",
            profile: &CoupleProfile{
                Partner1: &Partner{Name: "张三"},
            },
            wantErr: true,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            err := tt.profile.Validate()
            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
            }
        })
    }
}
```

### 运行测试
```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./backend/models

# 运行测试并显示覆盖率
go test -cover ./...

# 生成覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

## 📦 构建和部署

### 构建配置
```json
// wails.json
{
  "name": "love-software",
  "outputfilename": "爱情个人软件",
  "frontend": {
    "dir": "./frontend",
    "install": "npm install",
    "build": "npm run build"
  },
  "info": {
    "productName": "爱情个人软件",
    "productVersion": "1.0.0",
    "copyright": "Copyright © 2024"
  }
}
```

### 跨平台构建
```bash
# Windows
wails build -platform windows/amd64

# macOS
wails build -platform darwin/amd64
wails build -platform darwin/arm64

# Linux
wails build -platform linux/amd64
```

### 打包优化
```bash
# 启用UPX压缩
wails build -upx

# 禁用控制台窗口（Windows）
wails build -windowsconsole false

# 自定义图标
wails build -icon icon.ico
```

## 🐛 调试技巧

### 前端调试
```javascript
// 在浏览器开发者工具中调试
console.log('调试信息:', data)

// 使用Wails运行时API
import { LogInfo, LogError } from '../wailsjs/runtime/runtime'

LogInfo('前端日志信息')
LogError('前端错误信息')
```

### 后端调试
```go
// 使用标准日志
import "log"

log.Printf("调试信息: %+v", data)

// 使用Wails日志
import "github.com/wailsapp/wails/v2/pkg/logger"

func (a *App) startup(ctx context.Context) {
    a.ctx = ctx
    logger.NewDefaultLogger().Info("应用启动成功")
}
```

## 📚 学习资源

### 官方文档
- [Wails官方文档](https://wails.io/docs/introduction)
- [Go官方文档](https://golang.org/doc/)
- [Tailwind CSS文档](https://tailwindcss.com/docs)

### 推荐教程
- [Go语言圣经](https://gopl-zh.github.io/)
- [现代JavaScript教程](https://zh.javascript.info/)
- [CSS Grid完全指南](https://css-tricks.com/snippets/css/complete-guide-grid/)

### 社区资源
- [Wails社区](https://github.com/wailsapp/wails/discussions)
- [Go语言中文网](https://studygolang.com/)
- [前端开发者手册](https://frontendmasters.com/books/front-end-handbook/)
